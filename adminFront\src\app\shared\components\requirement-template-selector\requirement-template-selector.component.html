<!-- 需求模板選擇器共用元件 -->
<nb-card class="requirement-template-dialog">
  <nb-card-header class="requirement-template-header">
    <div class="requirement-template-title">需求項目選擇器</div>
    <button class="close-btn" nbButton ghost (click)="close()">
      <nb-icon icon="close-outline"></nb-icon>
    </button>
  </nb-card-header>

  <nb-card-body class="requirement-template-body">
    <!-- 搜尋區域 -->
    <div class="search-section">
      <div class="section-title">
        <nb-icon icon="search-outline" class="mr-2"></nb-icon>搜尋條件
      </div>
      
      <div class="search-form">
        <div class="row">
          <div class="col-md-4">
            <label class="search-label">區域</label>
            <input type="text" nbInput 
                   placeholder="請輸入區域" 
                   [(ngModel)]="searchFilters.CLocation">
          </div>
          <div class="col-md-4">
            <label class="search-label">工程項目</label>
            <input type="text" nbInput 
                   placeholder="請輸入工程項目" 
                   [(ngModel)]="searchFilters.CRequirement">
          </div>
          <div class="col-md-4">
            <label class="search-label">狀態</label>
            <nb-select [(ngModel)]="searchFilters.CStatus" placeholder="請選擇狀態">
              <nb-option *ngFor="let status of statusOptions" [value]="status.value">
                {{ status.label }}
              </nb-option>
            </nb-select>
          </div>
        </div>
        
        <div class="row mt-3">
          <div class="col-md-4">
            <label class="search-label">房屋類型</label>
            <nb-select [(ngModel)]="searchFilters.CHouseType" 
                       placeholder="請選擇類型" 
                       multiple>
              <nb-option *ngFor="let type of houseTypeOptions" [value]="type.value">
                {{ type.label }}
              </nb-option>
            </nb-select>
          </div>
          <div class="col-md-4">
            <label class="search-label">預約需求</label>
            <nb-select [(ngModel)]="searchFilters.CIsShow" placeholder="全部">
              <nb-option [value]="null">全部</nb-option>
              <nb-option [value]="true">是</nb-option>
              <nb-option [value]="false">否</nb-option>
            </nb-select>
          </div>
          <div class="col-md-4">
            <label class="search-label">簡易客變</label>
            <nb-select [(ngModel)]="searchFilters.CIsSimple" placeholder="全部">
              <nb-option [value]="null">全部</nb-option>
              <nb-option [value]="true">是</nb-option>
              <nb-option [value]="false">否</nb-option>
            </nb-select>
          </div>
        </div>
        
        <div class="search-actions mt-3">
          <button nbButton status="basic" (click)="onReset()" class="mr-2">
            <nb-icon icon="refresh-outline"></nb-icon>重置
          </button>
          <button nbButton status="primary" (click)="onSearch()">
            <nb-icon icon="search-outline"></nb-icon>搜尋
          </button>
        </div>
      </div>
    </div>

    <!-- 選擇區域 -->
    <div class="selection-section">
      <div class="section-title">
        <nb-icon icon="list-outline" class="mr-2"></nb-icon>選擇項目
        <span class="selected-count" *ngIf="getSelectedItems().length > 0">
          (已選擇 {{ getSelectedItems().length }} 項)
        </span>
      </div>

      <!-- 全選控制 -->
      <div class="select-all-control" *ngIf="multiple && requirements.length > 0">
        <nb-checkbox [ngModel]="requirements.length > 0 && requirements.every(item => item.selected)"
                     [indeterminate]="requirements.some(item => item.selected) && !requirements.every(item => item.selected)"
                     (ngModelChange)="toggleSelectAll($event)">
          全選當前頁面
        </nb-checkbox>
      </div>

      <!-- 載入中狀態 -->
      <div *ngIf="isLoading" class="loading-state">
        <nb-icon icon="loader-outline" class="spinning"></nb-icon>
        載入中...
      </div>

      <!-- 需求項目列表 -->
      <div *ngIf="!isLoading && requirements.length > 0" class="requirement-list">
        <div *ngFor="let requirement of requirements" 
             class="requirement-item"
             [class.selected]="requirement.selected">
          <div class="requirement-checkbox">
            <nb-checkbox [(ngModel)]="requirement.selected" 
                         (ngModelChange)="toggleItemSelection(requirement)">
            </nb-checkbox>
          </div>
          <div class="requirement-info">
            <div class="requirement-main">
              <div class="requirement-name">{{ requirement.CRequirement }}</div>
              <div class="requirement-location">{{ requirement.CLocation || '-' }}</div>
            </div>
            <div class="requirement-details">
              <div class="detail-item">
                <span class="detail-label">類型:</span>
                <span class="detail-value">{{ getHouseTypeText(requirement.CHouseType) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">單價:</span>
                <span class="detail-value">{{ requirement.CUnitPrice | number:'1.0-2' }} {{ requirement.CUnit || '' }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">狀態:</span>
                <span class="detail-value">{{ getStatusText(requirement.CStatus) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">預約需求:</span>
                <span class="detail-value">{{ getIsShowText(requirement.CIsShow) }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">簡易客變:</span>
                <span class="detail-value">{{ getIsSimpleText(requirement.CIsSimple) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 無資料狀態 -->
      <div *ngIf="!isLoading && requirements.length === 0" class="no-data-state">
        <nb-icon icon="inbox-outline" class="no-data-icon"></nb-icon>
        <div class="no-data-text">沒有找到符合條件的需求項目</div>
      </div>

      <!-- 分頁控制 -->
      <div *ngIf="totalRecords > pageSize" class="pagination-section">
        <div class="pagination-info">
          共 {{ totalRecords }} 筆資料，第 {{ currentPage }} 頁
        </div>
        <div class="pagination-controls">
          <button nbButton size="small" status="basic" 
                  [disabled]="currentPage <= 1"
                  (click)="onPageChange(currentPage - 1)">
            <nb-icon icon="chevron-left-outline"></nb-icon>
          </button>
          <span class="page-info">{{ currentPage }} / {{ Math.ceil(totalRecords / pageSize) }}</span>
          <button nbButton size="small" status="basic"
                  [disabled]="currentPage >= Math.ceil(totalRecords / pageSize)"
                  (click)="onPageChange(currentPage + 1)">
            <nb-icon icon="chevron-right-outline"></nb-icon>
          </button>
        </div>
      </div>
    </div>

    <!-- 選擇摘要 -->
    <div *ngIf="getSelectedItems().length > 0" class="selection-summary">
      <div class="summary-title">選擇摘要</div>
      <div class="summary-content">
        <div class="summary-item">
          <span class="summary-label">已選擇項目:</span>
          <span class="summary-value">{{ getSelectedItems().length }} 項</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">總金額:</span>
          <span class="summary-value total-price">{{ getTotalPrice() | number:'1.0-2' }}</span>
        </div>
      </div>
    </div>
  </nb-card-body>

  <!-- 操作按鈕 -->
  <nb-card-footer class="requirement-template-footer">
    <div class="footer-actions">
      <button nbButton status="basic" (click)="close()" class="mr-2">
        取消
      </button>
      <button nbButton status="primary" 
              (click)="confirmSelection()"
              [disabled]="getSelectedItems().length === 0">
        確認選擇 ({{ getSelectedItems().length }})
      </button>
    </div>
  </nb-card-footer>
</nb-card>
