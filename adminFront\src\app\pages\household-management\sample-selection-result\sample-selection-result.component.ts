import { Component, OnInit } from '@angular/core';
import { NbDialogService } from '@nebular/theme';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { FileService, FinalDocumentService, HouseService } from 'src/services/api/services';
import { CreateFinalDocArgs, GetListFinalDocRes, TblHouse } from 'src/services/api/models';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'src/app/shared/services/message.service';
import { BaseComponent } from '../../components/base/baseComponent';
import { Location } from '@angular/common';
import { EventService, EEvent } from 'src/app/shared/services/event.service';
import * as moment from 'moment';

@Component({
  selector: 'ngx-sample-selection-result',
  templateUrl: './sample-selection-result.component.html',
  styleUrls: ['./sample-selection-result.component.scss'],
})

export class SampleSelectionResultComponent extends BaseComponent implements OnInit {
  constructor(
    private _allow: AllowHelper,
    private dialogService: NbDialogService,
    private valid: ValidationHelper,
    private _finalDocumentService: FinalDocumentService,
    private message: MessageService,
    private route: ActivatedRoute,
    private location: Location,
    private _eventService: EventService,
    private _houseService: HouseService,
    private fileService: FileService
  ) {
    super(_allow)
  }

  houseID: any
  buildCaseId: number

  override ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      if (params) {
        this.buildCaseId = +(params.get('id1') ?? 0);
        this.houseID = +(params.get('id2') ?? 0);
        if (this.houseID) {
          this.getHouseById()
        }
        this.getListFinalDoc()
      }
    });
  }

  houseByID: TblHouse

  getHouseById() {
    this._houseService.apiHouseGetHouseByIdPost$Json({
      body: { CHouseID: this.houseID }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.houseByID = res.Entries
      }
    })
  }

  documentStatusOptions = ['待審核', '已駁回', '待客戶簽回', '客戶已簽回']
  openPdfInNewTab(data?: any) {
    if (data && data.CFile) {
      // 使用 FileService.GetFile 取得檔案 blob
      this.fileService.getFile(data.CFile, data.CDocumentName || 'document.pdf').subscribe({
        next: (blob: Blob) => {
          // 建立 blob URL
          const url = URL.createObjectURL(blob);
          // 在新分頁開啟 PDF
          window.open(url, '_blank');
          // 延遲清理 URL 以確保檔案能正確載入
          setTimeout(() => URL.revokeObjectURL(url), 10000);
        },
        error: (error: any) => {
          console.error('取得檔案失敗:', error);
          this.message.showErrorMSG('無法開啟檔案，請稍後再試');
        }
      });
    }
  }


  listFinalDoc: GetListFinalDocRes[]


  getListFinalDoc() {
    this._finalDocumentService.apiFinalDocumentGetListFinalDocPost$Json({
      body: {
        CHouseID: this.houseID,
        PageIndex: this.pageIndex,
        PageSize: this.pageSize,
      }
    }).subscribe(res => {
      if (res.TotalItems && res.Entries && res.StatusCode == 0) {
        this.listFinalDoc = res.Entries! ?? []
        this.totalRecords = res.TotalItems;
      }
    })
  }


  listSpecialChangeAvailable: any[]
  getListSpecialChangeAvailable() {
    this._finalDocumentService.apiFinalDocumentGetListSpecialChangeAvailablePost$Json({
      body: {
        CHouseID: this.houseID,
      }
    }).subscribe(res => {
      this.listSpecialChangeAvailable = []
      if (res.TotalItems && res.Entries && res.StatusCode == 0) {
        this.listSpecialChangeAvailable = res.Entries! ?? []
        if (res.Entries.length) {
          this.listSpecialChangeAvailable = res.Entries.map((e: any) => {
            return { ...e, isChecked: false }
          })
        }
      }
    })
  }
  finalDoc: CreateFinalDocArgs

  validation() {
    this.valid.clear();
    this.valid.required('[文件名稱]', this.finalDoc.CDocumentName)
    this.valid.required('[送審資訊]', this.finalDoc.CApproveRemark)
    this.valid.required('[系統操作說明]', this.finalDoc.CNote)
  }

  goBack() {
    this._eventService.push({
      action: EEvent.GET_BUILDCASE,
      payload: this.buildCaseId
    })
    this.location.back()
  }

  getCheckedCIDs(changeArray: any[]) {
    if (changeArray && changeArray.length) {
      return changeArray.filter(change => change.isChecked).map(change => change.CID);
    } return []
  }

  onCreateFinalDoc(ref: any) {
    this.validation()
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return
    }
    const param = {
      ...this.finalDoc,
      CSpecialChange: this.getCheckedCIDs(this.listSpecialChangeAvailable)
    }
    this._finalDocumentService.apiFinalDocumentCreateFinalDocPost$Json({
      body: param
    }).subscribe(res => {
      if (res.StatusCode === 0) {
        this.getListFinalDoc();
        this.message.showSucessMSG("執行成功");
        ref.close();
      } else {
        this.message.showErrorMSG(res.Message!)
      }
    })
  }

  isChecked = true
  pageChanged(newPage: number) {
    this.pageIndex = newPage;
    this.getListFinalDoc();
  }


  addNew(ref: any) {
    this.finalDoc = {
      CHouseID: this.houseID,
      CDocumentName: '',
      CApproveRemark: '',
      CNote: ""
    }
    this.getListSpecialChangeAvailable()
    this.dialogService.open(ref)
  }

  onOpenModel(ref: any) {
    this.dialogService.open(ref)
  }

  onClose(ref: any) {
    ref.close();
  }

  formatDate(date: string) {
    return moment(date).format('YYYY/MM/DD HH:mm');
  }
}
